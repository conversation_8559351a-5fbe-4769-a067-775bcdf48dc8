"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  Plus,
  Wallet,
  Users,
  Award,
  Gift,
  HelpCircle,
  ChevronRight,
  Star,
  Shield,
  ChevronDown,
  Sun,
  Moon,
  ArrowLeft,
  Share2,
  User,
  <PERSON>,
  <PERSON>,
} from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import LanguageSelector from "@/components/language-selector"
import { useLanguage } from "@/contexts/language-context"
import { useAuth } from "@/contexts/auth-context"
import Image from "next/image"
import AuthStatus<PERSON>hecker from "./auth-status-checker"
import { useRouter } from "next/navigation"

interface DashboardLayoutProps {
  children: React.ReactNode
  activeTab: string
  onTabChange: (tab: string) => void
}

export default function DashboardLayout({ children, activeTab, onTabChange }: DashboardLayoutProps) {
  const [isSidebarO<PERSON>, setIsSidebarOpen] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t, isRTL } = useLanguage()
  const { user, isAuthenticated, getUserProfile } = useAuth()
  const pathname = usePathname()
  const [userProfile, setUserProfile] = useState<any>(null)
  const router = useRouter();

  // Fetch user profile for header display
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (isAuthenticated && getUserProfile) {
        try {
          const profile = await getUserProfile()
          setUserProfile(profile)
        } catch (error) {
          console.error('Failed to fetch user profile for header:', error)
        }
      }
    }

    fetchUserProfile()
  }, [isAuthenticated, getUserProfile])

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Check localStorage first, then system preference
      const savedTheme = localStorage.getItem("theme")
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      const shouldUseDark = savedTheme ? savedTheme === "dark" : prefersDark
      
      setIsDarkMode(shouldUseDark)
      // Apply initial theme
      if (shouldUseDark) {
        document.documentElement.classList.add("dark")
      } else {
        document.documentElement.classList.remove("dark")
      }
    }
  }, [])

  const toggleTheme = () => {
    const newDarkMode = !isDarkMode
    setIsDarkMode(newDarkMode)
    document.documentElement.classList.toggle("dark")
    // Save theme preference to localStorage
    localStorage.setItem("theme", newDarkMode ? "dark" : "light")
  }

  const menuItems = [
    // Core Trading Section
    {
      id: "overview",
      label: t("dashboard.overview"),
      icon: BarChart3,
      badge: null,
      description: "Account overview and analytics"
    },
    {
      id: "new-challenge",
      label: t("dashboard.newChallenge"),
      icon: Plus,
      badge: null,
      description: "Start a new trading challenge"
    },
    {
      id: "account-details",
      label: "Account Details",
      icon: Key,
      badge: null,
      description: "Manage trading account credentials"
    },

    // Financial Section
    {
      id: "withdraw",
      label: t("dashboard.withdraw"),
      icon: Wallet,
      badge: null,
      description: "Withdraw your profits"
    },
    {
      id: "kyc",
      label: "KYC Verification",
      icon: Shield,
      badge: null,
      description: "Complete identity verification to unlock withdrawals"
    },

    // Partnership & Growth Section
    {
      id: "referral",
      label: t("dashboard.referral"),
      icon: Users,
      badge: null,
      description: "Invite friends and earn bonuses"
    },
    {
      id: "affiliate",
      label: t("dashboard.affiliate"),
      icon: Award,
      badge: null,
      description: "Partner program dashboard"
    },

    // Information Section
    {
      id: "faqs",
      label: t("dashboard.faqs"),
      icon: HelpCircle,
      badge: null,
      description: "Frequently asked questions"
    },


    {
      id: "social-media",
      label: "Social Media",
      icon: Share2,
      badge: null,
      description: "Connect with our community"
    },
    {
      id: "giveaway",
      label: "Giveaway",
      icon: Gift,
      badge: null,
      description: "Participate in trading giveaways"
    },
  ]

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Custom CSS for subtle scrollbar without arrows */}
      <style jsx>{`
        .sidebar-scroll::-webkit-scrollbar {
          width: 4px;
        }
        .sidebar-scroll::-webkit-scrollbar-track {
          background: transparent;
        }
        .sidebar-scroll::-webkit-scrollbar-thumb {
          background: rgba(156, 163, 175, 0.3);
          border-radius: 2px;
        }
        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
          background: rgba(156, 163, 175, 0.5);
        }
        .sidebar-scroll::-webkit-scrollbar-button {
          display: none;
        }
        .sidebar-scroll {
          scrollbar-width: thin;
          scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
        }
      `}</style>

      {/* Top Header Bar - Fixed at top */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-gray-800 to-gray-900 dark:from-gray-900 dark:to-gray-800 text-white px-6 py-4 flex items-center justify-between shadow-lg border-b border-gray-700 dark:border-gray-600">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 flex items-center justify-center shadow-lg border-2 border-white/20 bg-white/10 backdrop-blur-sm">
              <Image
                src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
                alt="Forex Throne Logo"
                width={32}
                height={32}
                className="w-full h-full object-contain"
                priority
              />
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">FxThrone</span>
              <span className="text-xs text-gray-300 font-medium">Trading Platform</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 cursor-pointer">
            <span className="text-white">
              {user?.firstName && user?.lastName
                ? `${user.firstName} ${user.lastName}`
                : userProfile?.name
                  ? userProfile.name
                  : user?.firstName || 'User'
              }
            </span>
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">
                {user?.firstName && user?.lastName
                  ? (user.firstName[0] + user.lastName[0]).toUpperCase()
                  : userProfile?.name
                    ? userProfile.name.split(' ').map((n: string) => n[0]).join('').toUpperCase().substring(0, 2)
                    : 'U'
                }
              </span>
            </div>
            <Bell className="w-5 h-5 text-white" />
          </div>
        </div>
      </div>

      {/* Main Content Area - Below header */}
      <div className="flex pt-16"> {/* pt-16 adds space below fixed header */}
        {/* Left Sidebar - Fixed/Sticky with internal scroll */}
        <div className="fixed left-4 top-20 w-64 bg-white dark:bg-gray-800 h-[calc(100vh-5rem)] border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden">
          <div className="h-full overflow-y-auto p-6 sidebar-scroll">
            {/* New Challenge Button */}
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white mb-6 py-3 px-4 rounded-lg transition-colors duration-200 font-medium text-sm">
              <Plus className="w-4 h-4 mr-2" />
              New Challenge
            </Button>

            {/* Navigation Menu */}
            <nav className="space-y-1">
              {menuItems.slice(0, 12).map((item) => {
                const Icon = item.icon
                const currentPath = pathname?.split('/').pop() || 'overview'
                const isActive = currentPath === item.id || (currentPath === 'overview' && item.id === 'overview')
                
                return (
                  <Link
                    key={item.id}
                    href={`/dashboard/${item.id === 'overview' ? 'overview' : item.id}`}
                    onClick={() => {
                      onTabChange(item.id)
                      setIsSidebarOpen(false)
                    }}
                    className={`flex items-center gap-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 group ${
                      isActive
                        ? "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/40 dark:to-blue-800/40 text-blue-700 dark:text-blue-400 shadow-sm border border-blue-200 dark:border-blue-700"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white"
                    }`}
                  >
                    <div className={`p-2 rounded-lg transition-all duration-300 ${
                      isActive
                        ? "bg-blue-100 dark:bg-blue-800/50 text-blue-600 dark:text-blue-400"
                        : "bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 group-hover:bg-blue-50 dark:group-hover:bg-blue-900/30 group-hover:text-blue-600 dark:group-hover:text-blue-400"
                    }`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    <span className="flex-1 text-left font-medium">{item.label}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="text-xs bg-red-500 hover:bg-red-600 text-white border-0">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                )
              })}
            </nav>

            {/* More Menu Items Dropdown */}
            {menuItems.length > 12 && (
              <div className="mt-4">
                <details className="group">
                  <summary className="flex items-center gap-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-all duration-300">
                    <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400">
                      <ChevronDown className="w-4 h-4 group-open:rotate-180 transition-transform" />
                    </div>
                    <span className="flex-1 text-left">More</span>
                  </summary>
                  <nav className="mt-2 space-y-1">
                    {menuItems.slice(12).map((item) => {
                      const Icon = item.icon
                      const currentPath = pathname?.split('/').pop() || 'overview'
                      const isActive = currentPath === item.id
                      
                      return (
                        <Link
                          key={item.id}
                          href={`/dashboard/${item.id}`}
                          onClick={() => {
                            onTabChange(item.id)
                            setIsSidebarOpen(false)
                          }}
                          className={`flex items-center gap-3 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 group ml-4 ${
                            isActive
                              ? "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/40 dark:to-blue-800/40 text-blue-700 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white"
                          }`}
                        >
                          <div className={`p-1.5 rounded-md transition-all duration-300 ${
                            isActive
                              ? "bg-blue-100 dark:bg-blue-800/50 text-blue-600 dark:text-blue-400"
                              : "bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 group-hover:bg-blue-50 dark:group-hover:bg-blue-900/30 group-hover:text-blue-600 dark:group-hover:text-blue-400"
                          }`}>
                            <Icon className="w-3 h-3" />
                          </div>
                          <span className="flex-1 text-left text-xs">{item.label}</span>
                          {item.badge && (
                            <Badge variant="secondary" className="text-xs bg-red-500 hover:bg-red-600 text-white border-0">
                              {item.badge}
                            </Badge>
                          )}
                        </Link>
                      )
                    })}
                  </nav>
                </details>
              </div>
            )}

            {/* Settings & Preferences */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 space-y-2">
              <button 
                onClick={toggleTheme}
                className="w-full flex items-center gap-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 group"
              >
                <div className={`p-2 rounded-lg transition-all duration-300 ${
                  isDarkMode 
                    ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400" 
                    : "bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 group-hover:bg-yellow-50 dark:group-hover:bg-yellow-900/30 group-hover:text-yellow-600 dark:group-hover:text-yellow-400"
                }`}>
                  {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
                </div>
                <span className="flex-1 text-left">{isDarkMode ? "Light mode" : "Dark mode"}</span>
              </button>
              
              <Link href="/">
                <button className="w-full flex items-center gap-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 group">
                  <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 group-hover:bg-blue-50 dark:group-hover:bg-blue-900/30 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-300">
                    <ArrowLeft className="w-4 h-4" />
                  </div>
                  <span className="flex-1 text-left">Back to website</span>
                  <ChevronRight className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Main Content Area - With proper spacing and margin for fixed sidebar */}
        <div className="flex-1 bg-gray-100 dark:bg-gray-900 min-h-screen ml-72 mr-4 mt-4">
          <div className="p-8">
            {/* Breadcrumb */}
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Trader / {menuItems.find(item => item.id === activeTab)?.label || "Overview"}
            </div>
            
            {/* Page Content */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm">
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Chat Bubble */}
      <div className="fixed bottom-6 right-6">
        <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-blue-600 transition-colors">
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-white rounded-full animate-bounce"></div>
            <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>

      {/* Starburst Icon */}
      <div className="fixed top-4 right-4">
        <div className="w-8 h-8 bg-yellow-400 rounded-lg flex items-center justify-center shadow-lg">
          <Star className="w-4 h-4 text-yellow-800" />
        </div>
      </div>

      {/* Auth Status Checker */}
      <AuthStatusChecker />
    </div>
  )
}
