"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Moon, Sun, Zap, Users, Award, Globe, User, LogIn, Gift } from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useLanguage } from "@/contexts/language-context"

export default function Navbar() {
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t } = useLanguage()
  const pathname = usePathname()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      setIsDarkMode(prefersDark)
    }
  }, [])

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  // Hide navbar on dashboard pages
  if (pathname?.startsWith('/dashboard')) {
    return null
  }

  return (
    <nav
      className="fixed top-2 sm:top-4 left-2 sm:left-4 right-2 sm:right-4 z-40 flex items-center justify-between bg-transparent backdrop-blur-sm border border-gray-200/30 dark:border-white/10 rounded-xl sm:rounded-2xl px-2 sm:px-3 py-1.5 sm:py-2 shadow-sm"
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Logo */}
      <Link href="/" className="flex items-center gap-1.5 sm:gap-2 group">
        <div className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <Image
            src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
            alt="Forex Throne Logo"
            width={40}
            height={40}
            className="w-full h-full object-contain"
            priority
          />
        </div>
        <div className="hidden sm:block">
          <h1 className="text-sm sm:text-lg md:text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            Forex Throne
          </h1>
          <p className="text-xs text-gray-600 dark:text-white/60">Prop Trading Firm</p>
        </div>
      </Link>

      {/* Navigation Links */}
      <div className="flex items-center gap-1 sm:gap-2 md:gap-3">
        <Button
          variant="ghost"
          onClick={toggleTheme}
          className="text-xs sm:text-sm font-normal text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-2 py-1.5 rounded-lg group"
          aria-label="Toggle between light and dark theme"
        >
          <div className="group-hover:rotate-180 transition-transform duration-500">
            {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </div>
        </Button>

        {/* Direct Navigation Links */}
        <Link href="/how-it-works" className="hidden sm:block">
          <Button
            variant="ghost"
            className="text-xs sm:text-sm font-normal text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-2 py-1.5 rounded-lg"
          >
            <Zap className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden lg:inline">How It Works</span>
            <span className="lg:hidden">How</span>
          </Button>
        </Link>

        <Link href="/challenges" className="hidden md:block">
          <Button
            variant="ghost"
            className="text-xs sm:text-sm font-normal text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-2 py-1.5 rounded-lg"
          >
            <Award className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            Challenges
          </Button>
        </Link>

        <Link href="/faqs" className="hidden lg:block">
          <Button
            variant="ghost"
            className="text-xs sm:text-sm font-normal text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-2 py-1.5 rounded-lg"
          >
            <Globe className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            FAQs
          </Button>
        </Link>

        <Link href="/affiliate" className="hidden xl:block">
          <Button
            variant="ghost"
            className="text-xs sm:text-sm font-normal text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-2 py-1.5 rounded-lg"
          >
            <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            Affiliate
          </Button>
        </Link>

        <Link href="/giveaway" className="hidden xl:block">
          <Button
            variant="ghost"
            className="text-xs sm:text-sm font-normal text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-2 py-1.5 rounded-lg"
          >
            <Gift className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            Giveaway
          </Button>
        </Link>

        {/* Auth Buttons */}
        <Link href="/auth">
          <Button
            variant="ghost"
            className="text-xs sm:text-sm font-normal text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-2 sm:px-3 py-1.5 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            <LogIn className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">{t("nav.login")}</span>
            <span className="sm:hidden">Login</span>
          </Button>
        </Link>
        <Link href="/auth?mode=signup">
          <Button className="rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-2 sm:px-3 md:px-4 py-1.5 text-xs sm:text-sm font-medium hover:scale-105 transition-all duration-300 hover:shadow-lg shadow-md">
            <User className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">{t("nav.getFunded")}</span>
            <span className="sm:hidden">Start</span>
          </Button>
        </Link>
      </div>
    </nav>
  )
} 